'use client';

import { useEffect } from 'react';
import ErrorDisplay from '@/components/ErrorDisplay';

interface ErrorPageProps {
  readonly error: Error & { digest?: string };
  readonly reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    // Opcionalmente, registrar el error en un servicio de análisis
    console.error('Error no controlado:', error);
  }, [error]);

  return (
    <ErrorDisplay
      title="Algo salió mal"
      message={error.message || "Ha ocurrido un error inesperado."}
      code={error.digest}
      showHomeButton={true}
    />
  );
}
