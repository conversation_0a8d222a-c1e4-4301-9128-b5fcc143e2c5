'use client';

import React, { useState, useCallback } from 'react';
import { 
  TextField, 
  Paper, 
  Typography,
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Chip,
  IconButton,
  Box,
  Tooltip
} from '@mui/material';
import { 
  Search, 
  Article as ArticleIcon,
  UnfoldMore as UnfoldMoreIcon,
  AppsOutlined as AppsOutlinedIcon,
  ExpandMoreOutlined as ExpandMoreOutlinedIcon,
  DescriptionOutlined as DescriptionOutlinedIcon,
  NotificationsActive as NotificationsActiveIcon,
  ErrorOutlined as ErrorOutlinedIcon,
  AttachFileOutlined as AttachFileOutlinedIcon
} from '@mui/icons-material';

// Componente para el indicador de estado circular
const StatusIndicator = ({ status, progress }: { status: string; progress: number }) => {
  // Determinar el color basado en el estado
  let color;
  
  if (status === 'Finalizado') {
    color = '#2e7d32'; // Verde oscuro
  } else if (status === 'Casi listo') {
    color = '#1976d2'; // Azul
  } else if (status === 'En proceso') {
    color = '#1976d2'; // Azul
  } else {
    color = '#9e9e9e'; // Gris
  }
  
  const size = 36;
  const strokeWidth = 4;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (progress / 100) * circumference;
  
  return (
    <Box display="flex" alignItems="center" justifyContent="center" gap={2}>
      <Box position="relative" width={size} height={size}>
        {/* Círculo de fondo */}
        <Box
          component="svg"
          width={size}
          height={size}
          sx={{ transform: 'rotate(-90deg)' }}
        >
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="#e0e0e0"
            strokeWidth={strokeWidth}
          />
          {/* Círculo de progreso */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
          />
        </Box>
        
        {/* Texto de porcentaje en el centro */}
        <Box
          position="absolute"
          top={0}
          left={0}
          right={0}
          bottom={0}
          display="flex"
          alignItems="center"
          justifyContent="center"
        >
          <Typography 
            variant="caption" 
            sx={{ 
              fontSize: '10px', 
              fontWeight: 'bold',
              color: color
            }}
          >
            {progress}%
          </Typography>
        </Box>
      </Box>
      <Typography variant="body2">{status}</Typography>
    </Box>
  );
};

export default function TemasAgendaMisTareas() {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedTasks, setExpandedTasks] = useState<number[]>([]);

  // Datos de ejemplo ampliados con porcentaje de progreso
  const tasks = [
    { 
      id: 1, 
      title: 'Situación de locales en la Ciudad de María Auxiliadora', 
      priority: 'Importante', 
      dueDate: '16/05/2025', 
      status: 'En proceso',
      progress: 50,
      isParent: true,
      children: [2, 3, 4]
    },
    { 
      id: 2, 
      title: 'Revisión de contratos de alquiler', 
      priority: 'Urgente', 
      dueDate: '16/05/2025', 
      status: 'Finalizado',
      progress: 100,
      parentId: 1
    },
    { 
      id: 3, 
      title: 'Análisis de costos de mantenimiento', 
      priority: 'Importante', 
      dueDate: '16/05/2025', 
      status: 'En proceso',
      progress: 75,
      parentId: 1
    },
    { 
      id: 4, 
      title: 'Evaluación de nuevos inquilinos', 
      priority: 'Importante', 
      dueDate: '16/05/2025', 
      status: 'Pendiente',
      progress: 0,
      parentId: 1
    },
    { 
      id: 5, 
      title: 'Planificación estratégica 2026', 
      priority: 'Importante', 
      dueDate: '20/06/2025', 
      status: 'Pendiente',
      progress: 0,
      isParent: true,
      children: [6, 7]
    },
    { 
      id: 6, 
      title: 'Análisis de mercado regional', 
      priority: 'Importante', 
      dueDate: '10/06/2025', 
      status: 'En proceso',
      progress: 30,
      parentId: 5
    },
    { 
      id: 7, 
      title: 'Proyecciones financieras', 
      priority: 'Urgente', 
      dueDate: '15/06/2025', 
      status: 'Casi listo',
      progress: 90,
      parentId: 5
    }
  ];

  // Optimizar la función para evitar lag
  const toggleExpandTask = useCallback((taskId: number) => {
    setExpandedTasks(prev => 
      prev.includes(taskId) 
        ? prev.filter(id => id !== taskId) 
        : [...prev, taskId]
    );
  }, []);

  return (
    <div className="container mx-auto p-4 max-w-6xl font-contisans">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography 
          variant="h4" 
          gutterBottom 
          sx={{ 
            fontFamily: 'var(--font-ContiSans)', 
            fontWeight: 700,
            mb: 0
          }}
        >
          Mis Tareas del Directorio
        </Typography>
        
        <Typography 
          variant="body2"
          sx={{ 
            color: 'red',
            fontWeight: 500,
            cursor: 'pointer',
            p: 1,
            '&:hover': {
              textDecoration: 'underline',
              color: '#cc0000'
            }
          }}
        >
          Ir a la Vista Admin
        </Typography>
      </Box>
      
      <Typography 
        variant="body1" 
        gutterBottom 
        sx={{ 
          mb: 3, 
          fontFamily: 'var(--font-ContiSans)',
          fontWeight: 500,
          color: 'black'
        }}
      >
        Ingresá el tema, nombre de algún tema, tarea o documentos para buscar:
      </Typography>
      
      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 4, mb: 4, alignItems: 'center' }}>
        <Box sx={{ flex: 1, minWidth: 0, maxWidth: { md: '60%' } }}>
          <TextField
            fullWidth
            placeholder="Buscar..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ 
              '& .MuiInputBase-root': {
                paddingLeft: 1
              }
            }}
            slotProps={{
              input: {
                startAdornment: <Search fontSize="small" style={{ marginLeft: '8px', color: '#9e9e9e' }} />
              }
            }}
            variant="outlined"
            size="small"
          />
        </Box>
        <Box sx={{ display: 'flex', gap: 4, flexShrink: 0, justifyContent: 'flex-end'}}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2">Desde</Typography>
            <TextField
              type="date"
              size="small"
              sx={{ width: 170 }}
              slotProps={{
                input: {
                  sx: { padding: '3px' }
                }
              }}
            />
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2">Hasta</Typography>
            <TextField
              type="date"
              size="small"
              sx={{ width: 170 }}
              slotProps={{
                input: {
                  sx: { padding: '3px' }
                }
              }}
            />
          </Box>
        </Box>
      </Box>
      
      <Paper 
        elevation={0} 
        sx={{ 
          mb: 4, 
          p: 3, 
          bgcolor: '#ffebeb', 
          borderRadius: 1
        }}
      >
        <Typography 
          variant="subtitle1" 
          sx={{ 
            fontFamily: 'var(--font-ContiSans)'
          }}
        >
          <span style={{ fontWeight: 600 }}>Hoy:</span> Consejo de Directorio - 15:00 Hs - Sala de reunión - 8vo piso
        </Typography>
      </Paper>
      
      <TableContainer 
        component={Paper} 
        elevation={1} 
        sx={{ 
          borderRadius: 2, 
          overflow: 'hidden',
          '& .MuiTable-root': {
            minWidth: 750,
          }
        }}
      >
        <Table>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell width="40%" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                <Box display="flex" alignItems="center" gap={1}>
                  <UnfoldMoreIcon fontSize="small" />
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>Título</Typography>
                </Box>
              </TableCell>
              <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                  <UnfoldMoreIcon fontSize="small" />
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>Prioridad</Typography>
                </Box>
              </TableCell>
              <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                  <UnfoldMoreIcon fontSize="small" />
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>Vencimiento</Typography>
                </Box>
              </TableCell>
              <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                <Box display="flex" alignItems="center" justifyContent="center" gap={1}>
                  <UnfoldMoreIcon fontSize="small" />
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>Estado</Typography>
                </Box>
              </TableCell>
              <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>Acciones</Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tasks.map((task) => {
              const isParent = task.isParent;
              const isExpanded = expandedTasks.includes(task.id);
              const isChild = task.parentId !== undefined;
              
              // Only show children if parent is expanded
              if (isChild && !expandedTasks.includes(task.parentId)) {
                return null;
              }
              
              // Extract nested ternary for task icon
              let taskIcon;
              if (isParent) {
                taskIcon = (
                  <>
                    <AppsOutlinedIcon fontSize="small" sx={{ color: '#9e9e9e' }} />
                    <IconButton 
                      size="small" 
                      onClick={() => toggleExpandTask(task.id)}
                      sx={{ p: 0.5 }}
                    >
                      <ExpandMoreOutlinedIcon 
                        fontSize="small" 
                        sx={{ 
                          transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
                          transition: 'transform 0.3s'
                        }} 
                      />
                    </IconButton>
                    <ArticleIcon fontSize="small" sx={{ color: '#0033A0' }} />
                  </>
                );
              } else if (isChild) {
                taskIcon = <DescriptionOutlinedIcon fontSize="small" sx={{ color: '#C4D600', ml: 1 }} />;
              } else {
                taskIcon = <ArticleIcon fontSize="small" sx={{ color: '#0033A0' }} />;
              }
              
              return (
                <TableRow key={task.id} hover sx={isChild ? { backgroundColor: '#f9f9f9' } : {}}>
                  <TableCell sx={{ borderBottom: '1px solid #e0e0e0', pl: isChild ? 4 : 2 }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      {taskIcon}
                      <Typography variant="body2">{task.title}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                    <Chip 
                      label={task.priority} 
                      size="small"
                      icon={(() => {
                        if (task.priority === 'Urgente') {
                          return <NotificationsActiveIcon fontSize="small" sx={{ color: '#d32f2f !important' }} />;
                        } else if (task.priority === 'Importante') {
                          return <ErrorOutlinedIcon fontSize="small" sx={{ color: '#f2950a !important' }} />;
                        } else {
                          return undefined;
                        }
                      })()}
                      sx={{
                        backgroundColor: (() => {
                          if (task.priority === 'Urgente') return '#ffebee';
                          if (task.priority === 'Importante') return '#fff3e0';
                          return '#f5f5f5';
                        })(),
                        color: (() => {
                          if (task.priority === 'Urgente') return '#b71c1c';
                          if (task.priority === 'Importante') return '#e65100';
                          return '#424242';
                        })(),
                        border: (() => {
                          if (task.priority === 'Urgente') return '1px solid #ef5350';
                          if (task.priority === 'Importante') return '1px solid #ff9800';
                          return '1px solid #e0e0e0';
                        })(),
                        borderRadius: '16px',
                        fontWeight: 500,
                        '& .MuiChip-label': { px: 1 },
                        '& .MuiChip-icon': { ml: 1 }
                      }}
                    />
                  </TableCell>
                  <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>{task.dueDate}</TableCell>
                  <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                    <StatusIndicator status={task.status} progress={task.progress} />
                  </TableCell>
                  <TableCell align="center" sx={{ borderBottom: '1px solid #e0e0e0' }}>
                    <Tooltip 
                      title="Adjuntar archivo" 
                      arrow
                      placement="top"
                      sx={{
                        backgroundColor: '#555555',
                        color: '#ffffff',
                        '& .MuiTooltip-arrow': {
                          color: '#555555',
                        }
                      }}
                    >
                      <IconButton 
                        size="small" 
                        sx={{ 
                          color: '#0033A0',
                          border: '1px solid #e0e0e0',
                          backgroundColor: '#e6f0ff',
                          '&:hover': {
                            border: '1px solid #0033A0',
                            backgroundColor: '#d6e4ff'
                          }
                        }}
                      >
                        <AttachFileOutlinedIcon 
                          fontSize="small" 
                          sx={{ transform: 'rotate(45deg)' }} 
                        />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
}





