/**
 * Resultado de una operación de desencriptación
 */
export interface DecryptionResult {
  /** Indica si la operación fue exitosa */
  success: boolean;
  /** Datos desencriptados (solo presente si success es true) */
  data?: string;
  /** Mensaje de error (solo presente si success es false) */
  error?: string;
}

/**
 * Datos del usuario autenticado
 */
export interface UserData {
  /** Nombre completo del usuario */
  name: string;
  /** Correo electrónico del usuario */
  email: string;
  /** Nombre de usuario */
  username: string;
  /** Roles asignados al usuario */
  roles: string[];
}

/**
 * Propiedades para el componente MenuItemLink
 */
export interface MenuItemLinkProps {
  /** Elemento del menú */
  item: MenuItem;
  /** Indica si el elemento está activo */
  isActive: boolean;
  /** Indica si el sidebar está expandido */
  isOpen: boolean;
}

/**
 * Elemento de submenú en la barra lateral
 */
export type SubMenuItem = {
  /** Texto a mostrar */
  label: string;
  /** Ruta de navegación */
  path: string;
  /** Icono opcional */
  icon?: React.ReactNode;
  /** Indica si el elemento es solo para administradores */
  adminOnly?: boolean;
};

/**
 * Elemento principal del menú en la barra lateral
 */
export type MenuItem = {
  /** Texto a mostrar */
  label: string;
  /** Icono del elemento */
  icon: React.ReactNode;
  /** Ruta de navegación (opcional si tiene hijos) */
  path?: string;
  /** Elementos hijos del submenú */
  children?: SubMenuItem[];
  /** Indica si el elemento es solo para administradores */
  adminOnly?: boolean;
};

/**
 * Propiedades para el componente ErrorDisplay
 */
export interface ErrorDisplayProps {
  /** Título del error */
  readonly title?: string;
  /** Mensaje descriptivo del error */
  readonly message?: string;
  /** Código de error (puede ser numérico o string) */
  readonly code?: string | number;
  /** Indica si se debe mostrar el botón para volver al inicio */
  readonly showHomeButton?: boolean;
}
