import Image from 'next/image';

export default function HomePage() {
  return (
    <div className="relative min-h-[calc(100vh-64px)] flex flex-col">
      {/* Imagen de fondo con overlay */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/inicio-bg.webp"
          alt="Fondo Banco Continental"
          fill
          priority
          sizes="100vw"
          quality={90}
          className="object-cover"
        />
        <div className="absolute inset-0 bg-[#0033A0]/50"></div>
      </div>

      {/* Contenido principal */}
      <div className="relative z-10 flex-1 flex flex-col items-center justify-center px-6 py-12 text-white">
        <div className="max-w-3xl w-full bg-white/10 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white text-center">
            Bienvenido/a al <span className="text-[#C4D600]">Directorio Finansys</span>
          </h1>

          <p className="text-xl mb-8 text-white/90 text-center">
            Plataforma de gestión y seguimiento para las actividades del Consejo Directorio
          </p>

        </div>
      </div>
    </div>
  );
}
