import React from "react";
import NavbarClient from "./NavbarClient";
import { headers } from "next/headers";
import { decodeJWT } from "@/app/api/user-info/route";
import { UserData } from "@/types";

async function getUserData(): Promise<UserData | undefined> {
  try {
    const headersList = await headers();
    const jbpmToken = headersList.get('x-jbpm-token');
    
    if (!jbpmToken) {
      return undefined;
    }
    
    // Decodificar el token JWT
    const tokenData = decodeJWT(jbpmToken);
    
    if (!tokenData) {
      return undefined;
    }
    
    // Extraer la información del usuario
    return {
      name: tokenData.name ?? `${tokenData.given_name ?? ''} ${tokenData.family_name ?? ''}`.trim(),
      email: tokenData.email,
      username: tokenData.preferred_username,
      roles: tokenData.role ?? []
    };
  } catch (error) {
    console.error('Error getting user data in server component:', error);
    return undefined;
  }
}

export default async function Navbar() {
  // Obtener datos del usuario en el servidor
  const userData = await getUserData();

  return (
    <div className="flex flex-col">
      <div className="bg-[#204080] text-white">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Left Section: Logo + Name */}
            <div className="flex-1 flex items-center">
              <div className="w-12 md:w-auto">
                {/* Espacio reservado para el icono de hamburguesa en móvil */}
              </div>
              <img
                src="/images/principal-fondo-azullg.png"
                alt="continental"
                className="h-8 invisible md:visible"
              />
            </div>

            {/* Right Section: User and Logout */}
            <NavbarClient initialUserData={userData} />
          </div>
        </div>
      </div>
    </div>
  );
}
