import CryptoJS from 'crypto-js';
import { DecryptionResult } from '@/types';

export function decryptJBPM(jbpmToken: string): DecryptionResult {
  try {
    // Usa la misma clave que en el backend
    const keyJBPM = process.env.CRYPTO_KEY;
    if (!keyJBPM) {
      return {
        success: false,
        error: 'CRYPTO_KEY environment variable not set'
      };
    }
    const decryptedText = aesDecrypt(keyJBPM, jbpmToken);
    return {
      success: true,
      data: decryptedText
    };
  } catch (error) {
    console.error("Error decrypting JBPM token:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function aesDecrypt(keyOriginal: string, encryptedBase64: string) {
    if (!encryptedBase64) throw new Error("Valor a desencriptar nulo");

    // 1. Hashear la clave original
    const key = CryptoJS.SHA256(keyOriginal);

    // 2. <PERSON>rear un IV de 16 bytes en cero
    const iv = CryptoJS.lib.WordArray.create(new Uint8Array(16));

    // 3. Parsear texto cifrado desde Base64
    const encryptedBytes = CryptoJS.enc.Base64.parse(encryptedBase64);
    const encryptedCipherParams = CryptoJS.lib.CipherParams.create({
        ciphertext: encryptedBytes,
    });

    // 4. Desencriptar con AES-CBC y PKCS7
    const decrypted = CryptoJS.AES.decrypt(encryptedCipherParams, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });

    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    if (!decryptedText) throw new Error("Error al desencriptar");

    return decryptedText;
}