'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ErrorDisplayProps } from '@/types';

export default function ErrorDisplay({
  title = "Ha ocurrido un error",
  message = "Lo sentimos, no pudimos procesar su solicitud.",
  code,
  showHomeButton = true
}: Readonly<ErrorDisplayProps>) {
  return (
    <div className="min-h-[50vh] flex flex-col items-center justify-center p-6 font-contisans">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="bg-[#F8F9FA] p-6 flex justify-center">
          <Image 
            src="/images/abi4-error.svg" 
            alt="Error" 
            width={180} 
            height={180} 
            priority
            style={{ width: 'auto', height: 'auto' }}
          />
        </div>
        
        <div className="p-6 text-center">
          <h2 className="text-2xl font-bold text-[#161615] mb-2 flex items-center justify-center gap-2">
            {title}
            {code && (
              <span className="text-sm font-normal bg-[#F1F1F1] px-2 py-1 rounded">
                {typeof code === 'number' ? `Error ${code}` : code}
              </span>
            )}
          </h2>
          
          <p className="text-[#4A4B53] mb-6">{message}</p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            {showHomeButton && (
              <Link 
                href="/"
                className="px-5 py-2 bg-[#204080] text-white rounded hover:bg-[#0033A0] transition-colors"
              >
                Volver al inicio
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}






