import { NextRequest, NextResponse } from 'next/server';

// Function to decode JW<PERSON> without verification
export function decodeJWT(token: string) {
  try {
    // Split the token into parts
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    // Decode the payload (middle part)
    const payload = parts[1];
    const base64 = payload.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = Buffer.from(base64, 'base64').toString('utf8');
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT:', error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the token from the headers (this was set by the middleware)
    const jbpmToken = request.headers.get('x-jbpm-token');
    
    if (!jbpmToken) {
      return NextResponse.json(
        { error: 'No token found' },
        { status: 401 }
      );
    }

    // Decode the JWT token
    const tokenData = decodeJWT(jbpmToken);
    
    if (!tokenData) {
      return NextResponse.json(
        { error: 'Invalid token format' },
        { status: 400 }
      );
    }
    
    // Extract the user information
    const userData = {
      name: tokenData.name ?? `${tokenData.given_name ?? ''} ${tokenData.family_name ?? ''}`.trim(),
      email: tokenData.email,
      username: tokenData.preferred_username,
      roles: tokenData.role ?? []
    };

    return NextResponse.json(userData);
  } catch (error) {
    console.error('Error processing user info:', error);
    return NextResponse.json(
      { error: 'Failed to process user information' },
      { status: 500 }
    );
  }
}


