"use client";

import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MenuIcon from "@mui/icons-material/Menu";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import { Home, Lock, Assignment, PresentToAll } from "@mui/icons-material";
import {
  MenuItem,
  SubMenuItem as SubMenuItemType,
  MenuItemLinkProps,
} from "@/types";

// Componentes movidos fuera del componente principal
interface SubMenuItemProps {
  sub: SubMenuItemType;
  isActive: boolean;
  onNavigate: () => void;
}

const SubMenuItem: React.FC<SubMenuItemProps> = ({
  sub,
  isActive,
  onNavigate,
}) => {
  const subItemClass = isActive
    ? "block py-2 px-3 rounded mx-1 bg-[#46484E] text-white"
    : "block py-2 px-3 rounded mx-1 text-[#D0D0D0] hover:bg-[#46484E] hover:text-white";

  return (
    <Link
      key={sub.label}
      href={sub.path}
      onClick={onNavigate}
      className={`${subItemClass} flex items-center gap-2`}
      aria-current={isActive ? "page" : undefined}
    >
      {sub.icon && (
        <span className="flex-shrink-0" aria-hidden="true">
          {sub.icon}
        </span>
      )}
      <span className="flex-1">{sub.label}</span>
    </Link>
  );
};

interface MenuItemLinkComponentProps extends MenuItemLinkProps {
  onNavigate: () => void;
}

const MenuItemLinkComponent: React.FC<MenuItemLinkComponentProps> = ({
  item,
  isActive,
  isOpen,
  onNavigate,
}) => {
  return (
    <Link
      href={item.path ?? "/"}
      onClick={onNavigate}
      className={`
        ${
          isActive
            ? "bg-[#37393F] text-white"
            : "hover:bg-[#37393F] text-[#E0E0E0]"
        }
        ${
          isOpen
            ? "w-full flex items-center gap-2 py-2 px-3 rounded transition-colors text-sm whitespace-nowrap"
            : "md:flex md:flex-col md:items-center md:justify-center md:p-2 md:rounded md:w-12 md:h-12"
        }
      `}
      title={!isOpen ? item.label : undefined}
      aria-current={isActive ? "page" : undefined}
    >
      <span
        className={`text-lg flex-shrink-0 ${!isOpen && "md:m-0"}`}
        aria-hidden="true"
      >
        {item.icon}
      </span>
      {isOpen && <span className="flex-1 text-left">{item.label}</span>}
    </Link>
  );
};

export default function SidebarContent() {
  const [openItems, setOpenItems] = useState<string[]>([]);
  const [isOpen, setIsOpen] = useState(false); // Iniciar cerrado por defecto
  const [isMobile, setIsMobile] = useState(false);
  const pathname = usePathname();
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Detectar si estamos en móvil
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      // En desktop, abrir por defecto
      if (!mobile) {
        setIsOpen(true);
      }
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
    };
  }, []);

  // Cerrar el sidebar al navegar en móvil
  useEffect(() => {
    // Eliminamos esta lógica para evitar que se cierre automáticamente al navegar
    // Esto podría estar causando que el sidebar aparezca y desaparezca
  }, [pathname]);

  // Manejar clics fuera del sidebar (en móvil y desktop)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node) &&
        isOpen
      ) {
        // En móvil cerramos completamente, en desktop solo colapsamos
        if (isMobile) {
          setIsOpen(false);
        } else {
          // En desktop, solo colapsamos si el clic no fue en el botón de toggle
          const toggleButton = document.getElementById("sidebar-toggle-button");
          if (!toggleButton?.contains(event.target as Node)) {
            setIsOpen(false);
          }
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobile, isOpen]);

  const toggleItem = (label: string) => {
    setOpenItems((prev) =>
      prev.includes(label)
        ? prev.filter((item) => item !== label)
        : [...prev, label]
    );
  };

  const toggleSidebar = () => {
    // Usamos una función para garantizar que obtenemos el estado más reciente
    setIsOpen((currentState) => {
      const newState = !currentState;
      return newState;
    });
  };

  const closeSidebar = () => {
    if (isMobile) {
      setIsOpen(false);
    }
  };

  // Verificar si un ítem debe estar resaltado
  const isActive = (item: MenuItem): boolean => {
    if (item.path && pathname === item.path) return true;
    if (item.children) {
      return item.children.some((child) => pathname === child.path);
    }
    return false;
  };

  // Renderizar submenús
  const renderSubMenu = (item: MenuItem, isExpanded: boolean) => {
    if (!item.children || !isExpanded || !isOpen) return null;

    return (
      <div className="ml-6 mt-1 text-xs space-y-1 py-1 bg-[#37393F] rounded">
        {item.children.map((sub) => (
          <SubMenuItem
            key={sub.label}
            sub={sub}
            isActive={pathname === sub.path}
            onNavigate={closeSidebar}
          />
        ))}
      </div>
    );
  };

  // Extraer la lógica de clases para el botón
  const getButtonClass = (
    isExpanded: boolean,
    isItemActive: boolean,
    isOpen: boolean
  ): string => {
    let baseClass =
      "w-full flex items-center gap-2 py-2 px-3 rounded transition-colors text-sm whitespace-nowrap";

    // Determinar el estado visual
    if (isExpanded) {
      baseClass += " bg-[#37393F] text-white font-medium";
    } else if (isItemActive) {
      baseClass += " bg-[#37393F] text-white";
    } else {
      baseClass += " hover:bg-[#37393F] text-[#E0E0E0]";
    }

    // Añadir clases para el estado colapsado
    if (!isOpen) {
      baseClass +=
        " md:flex md:flex-col md:items-center md:justify-center md:p-2 md:w-12 md:h-12";
    }

    return baseClass;
  };

  // Función para obtener las clases del sidebar según el estado
  const getSidebarClasses = (isOpen: boolean, isMobile: boolean): string => {
    // En móvil, queremos una transición suave de entrada/salida
    if (isMobile) {
      return isOpen
        ? "w-72 translate-x-0 opacity-100"
        : "w-72 -translate-x-full opacity-0";
    }

    // En desktop, mantenemos el comportamiento actual
    return isOpen ? "w-72 translate-x-0" : "md:w-20 md:translate-x-0";
  };

  const menuItems: MenuItem[] = [
    { label: "Inicio", icon: <Home />, path: "/" },
    {
      label: "Secretaría de Directorio",
      icon: <PresentToAll />,
      children: [
        {
          label: "Temas y Agenda / Mis Tareas",
          path: "/secretaria-directorio/temas-agenda-mis-tareas",
          icon: <Assignment fontSize="small" />,
        },
        {
          label: "Asignación de roles",
          path: "/secretaria-directorio/asignacion-roles",
          icon: <Lock fontSize="small" />,
          adminOnly: true,
        },
      ],
    },
  ];

  return (
    <div className="relative h-full flex flex-col">
      {/* Botón de hamburguesa para móvil */}
      <button
        onClick={toggleSidebar}
        className="md:hidden fixed top-4 left-4 z-30 bg-[#204080] text-white p-2 rounded-md"
        aria-label={isOpen ? "Cerrar menú" : "Abrir menú"}
        aria-expanded={isOpen}
      >
        <MenuIcon aria-hidden="true" />
      </button>

      {/* Overlay para cerrar el sidebar - Solo en móvil */}
      {isMobile && (
        <div
          className={`
            fixed inset-0 z-20 bg-black
            transition-opacity duration-300 ease-in-out
            ${isOpen ? "opacity-50" : "opacity-0 pointer-events-none"}
          `}
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <nav
        ref={sidebarRef}
        className={`
          flex flex-col h-full
          fixed md:relative top-0 left-0 z-40
          bg-[#2D2F34] overflow-y-auto overflow-x-hidden
          transition-all duration-300 ease-in-out
          ${getSidebarClasses(isOpen, isMobile)}
        `}
        aria-label="Menú principal"
      >
        <div
          className={`flex-1 flex flex-col space-y-1 p-4 ${
            !isOpen && "md:items-center"
          }`}
        >
          {menuItems.map((item) => {
            const isItemActive = isActive(item);
            const isExpanded = openItems.includes(item.label);
            const buttonClass = getButtonClass(
              isExpanded,
              isItemActive,
              isOpen
            );

            return (
              <React.Fragment key={item.label}>
                <div
                  className={`mb-1 ${
                    !isOpen && "md:w-full md:flex md:justify-center"
                  }`}
                >
                  {item.path ? (
                    <MenuItemLinkComponent
                      item={item}
                      isActive={isItemActive}
                      isOpen={isOpen}
                      onNavigate={closeSidebar}
                    />
                  ) : (
                    <button
                      onClick={() => item.children && toggleItem(item.label)}
                      className={buttonClass}
                      title={!isOpen ? item.label : undefined}
                      aria-expanded={isExpanded}
                      aria-controls={`submenu-${item.label}`}
                    >
                      <span
                        className={`text-lg flex-shrink-0 ${
                          !isOpen && "md:m-0"
                        }`}
                        aria-hidden="true"
                      >
                        {item.icon}
                      </span>
                      {isOpen && (
                        <>
                          <span className="flex-1 text-left">{item.label}</span>
                          {item.children && (
                            <ExpandMoreIcon
                              className={`transform transition-transform flex-shrink-0 ${
                                isExpanded
                                  ? "rotate-180 text-white"
                                  : "text-[#E0E0E0]"
                              }`}
                              fontSize="small"
                              aria-hidden="true"
                            />
                          )}
                        </>
                      )}
                    </button>
                  )}

                  {/* Submenu con ID para accesibilidad */}
                  <div id={`submenu-${item.label}`}>
                    {renderSubMenu(item, isExpanded)}
                  </div>
                </div>

                {/* Separador */}
                <div className="h-px bg-[#3D3F44] my-2" aria-hidden="true" />
              </React.Fragment>
            );
          })}
        </div>

        {/* Botón para colapsar/expandir en la parte inferior del sidebar */}
        <div className="p-4 flex justify-center">
          <button
            id="sidebar-toggle-button"
            onClick={toggleSidebar}
            className="hidden md:flex bg-[#204080] text-white p-2 rounded-full shadow-md hover:bg-[#2a5bb3] transition-colors"
            aria-label={isOpen ? "Colapsar menú" : "Expandir menú"}
            aria-expanded={isOpen}
          >
            <ChevronRightIcon
              className={`transform transition-transform ${
                isOpen ? "rotate-180" : ""
              }`}
              aria-hidden="true"
            />
          </button>
        </div>
      </nav>
    </div>
  );
}
