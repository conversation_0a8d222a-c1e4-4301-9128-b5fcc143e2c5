'use client';

import React, { useState, useEffect } from 'react';
import LogoutIcon from '@mui/icons-material/Logout';
import { UserData } from '@/types';

// Helper function to get initials from name
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(part => part[0])
    .slice(0, 2)
    .join('')
    .toUpperCase();
};

// Recibir userData como prop desde el Server Component
export default function NavbarClient({ initialUserData }: Readonly<{ initialUserData?: UserData }>) {
  const [userData, setUserData] = useState<UserData | null>(initialUserData || null);
  const [isLoading, setIsLoading] = useState(!initialUserData);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Si ya tenemos datos iniciales, no necesitamos hacer fetch
    if (initialUserData) return;
    
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/user-info');
        
        if (response.ok) {
          const data = await response.json();
          setUserData(data);
          setError(null);
        } else {
          // Handle error responses
          const errorData = await response.json();
          console.error('Error response:', errorData);
          setError(errorData.error ?? 'Failed to fetch user data');
          
          if (response.status === 401) {
            console.warn('User is not authenticated');
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        setError('Network error when fetching user data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [initialUserData]);

  const handleLogout = () => {
    const logoutUrl = process.env.NEXT_PUBLIC_LOGOUT_URL ?? 'https://myapp.localhost:5001/Home/Principal';
    window.location.href = logoutUrl;
  };

  // Extract the display name logic to a separate function
  const getDisplayName = () => {
    if (isLoading) {
      return 'Cargando...';
    }
    
    if (error) {
      return 'Error de autenticación';
    }
    
    return userData?.name ?? 'Usuario desconocido';
  };

  // Create a function to get the user initials display
  const getUserInitialsDisplay = () => {
    if (isLoading) {
      return '...';
    }
    
    if (userData) {
      return getInitials(userData.name);
    }
    
    return '?';
  };

  return (
    <div className="flex items-center space-x-3">
      <div className="w-8 h-8 rounded-full bg-[#1a3366] flex items-center justify-center text-sm">
        {getUserInitialsDisplay()}
      </div>
      <span className="text-sm hidden md:inline">
        {getDisplayName()}
      </span>
      <button 
        onClick={handleLogout}
        className="text-white w-8 h-8 flex items-center justify-center rounded-full hover:bg-[#1a3366] transition-colors"
      >
        <LogoutIcon fontSize="small" />
      </button>
    </div>
  );
}
