import { NextRequest, NextResponse } from 'next/server';
import { decryptJBPM } from '@/utils/crypto';

export function middleware(request: NextRequest) {
  // Skip middleware for static files
  if (
    request.nextUrl.pathname.startsWith('/_next') ||
    request.nextUrl.pathname.startsWith('/favicon.ico')
  ) {
    return NextResponse.next();
  }

  // Get the cookie name from environment variables
  const tokenCookieName = process.env.JWT_COOKIE_NAME;

  // Get the JBPMToken cookie using the environment variable
  const jbpmToken = request.cookies
    .getAll()
    .find((cookie) => cookie.name === tokenCookieName);

  // If no token, redirect to login page
  if (!jbpmToken) {
    // Only redirect non-API routes to login
    if (!request.nextUrl.pathname.startsWith('/api/')) {
      const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL_FINANSYS;

      if (!loginUrl) {
        console.error("NEXT_PUBLIC_LOGIN_URL_FINANSYS is not defined in the environment variables.");
        return NextResponse.next();
      }

      return NextResponse.redirect(new URL(loginUrl, request.url));
    }
    
    // For API routes without token, just continue (the API will handle the 401)
    return NextResponse.next();
  }

  try {
    // Try to decrypt the token
    const decryptResult = decryptJBPM(jbpmToken.value);

    // If decryption was successful, add the decrypted token as a header
    if (decryptResult.success && decryptResult.data) {
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-jbpm-token', decryptResult.data);

      // Continue with the request, but with modified headers
      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    }
  } catch (error) {
    console.error('Error in middleware:', error);
  }

  // If there's an error or the token couldn't be decrypted, continue without modifying
  console.log('Referer URL:', request.headers.get('referer'));
  console.log('Request URL:', request.url);

  return NextResponse.next();
}

// Configure which routes the middleware will run on
export const config = {
  matcher: [
    // Include all routes except static files
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
