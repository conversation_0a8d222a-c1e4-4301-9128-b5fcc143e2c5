@import "tailwindcss";

/* Variables de colores Banco Continental */
:root {
  --color-primary: #0033A0;
  --color-accent: #C4D600;
  --color-text-primary: #343A40;
  --color-text-secondary: #6C757D;
  --color-bg-subtle: #F8F9FA;
  --color-border-light: #DEE2E6;
}

/* Estilos base */
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  padding: 0;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  background-color: #f9fafb;
  color: var(--color-text-primary);
}

img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}

input, button, textarea, select {
  font: inherit;
}

a {
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Personalización del scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-subtle);
}

::-webkit-scrollbar-thumb {
  background: var(--color-text-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Clases de utilidad personalizadas */
.bg-primary {
  background-color: var(--color-primary);
}

.text-primary {
  color: var(--color-primary);
}

.border-primary {
  border-color: var(--color-primary);
}

.bg-accent {
  background-color: var(--color-accent);
}

.text-accent {
  color: var(--color-accent);
}

.border-accent {
  border-color: var(--color-accent);
}

.bg-subtle {
  background-color: var(--color-bg-subtle);
}

.text-primary-color {
  color: var(--color-text-primary);
}

.text-secondary-color {
  color: var(--color-text-secondary);
}

.border-light {
  border-color: var(--color-border-light);
}

/* Botones personalizados */
.btn-primary {
  background-color: var(--color-primary);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: opacity 0.2s;
}

.btn-primary:hover {
  opacity: 0.9;
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s, color 0.2s;
}

.btn-secondary:hover {
  background-color: var(--color-primary);
  color: white;
}

.btn-accent {
  background-color: var(--color-accent);
  color: var(--color-text-primary);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: opacity 0.2s;
}

.btn-accent:hover {
  opacity: 0.9;
}

/* Tarjetas y contenedores */
.card {
  background-color: white;
  border: 1px solid var(--color-border-light);
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  padding: 1rem;
}

.card-hover:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
