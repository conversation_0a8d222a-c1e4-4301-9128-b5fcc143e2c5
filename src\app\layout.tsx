import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ContiSans } from "@/assets/fonts/fonts";
import Navbar from "@/components/Navbar";
import Sidebar from "@/components/Sidebar";

export const metadata: Metadata = {
  title: "Finansys Web - Consejo Directorio",
  description:
    "Plataforma web para la gestión y seguimiento de las actividades del Consejo Directorio de Finansys.",
  keywords: [
    "Finansys",
    "Consejo Directivo",
    "Gestión empresarial",
    "Plataforma web",
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es" className={ContiSans.variable}>
      <body className="font-contisans bg-gray-50 text-gray-900 overflow-x-hidden flex flex-col h-screen">
        <Navbar />
        <div className="flex flex-1 overflow-hidden">
          <Sidebar />
          <main className="flex-1 overflow-y-auto">{children}</main>
        </div>
      </body>
    </html>
  );
}
